# DocuSign - Document Management & Signing System

A comprehensive document signing system built with React, TypeScript, Tailwind CSS, and Supabase. Features user and admin roles, Google Drive integration, and real-time document workflow management.

## Features

### User Features
- **Document Upload**: Upload documents with subject, details, and urgency levels
- **Document Management**: View uploaded and signed documents in organized folders
- **Real-time Status**: Track document status (pending, signed, feedback)
- **Download**: Download original and signed documents
- **Feedback System**: Receive feedback from admins for missing details

### Admin Features
- **Validation Queue**: Review and manage all pending documents
- **Document Signing**: Upload signed versions of documents
- **Feedback System**: Provide feedback to users for incomplete documents
- **Document Management**: View, download, and organize all documents
- **User Management**: Track document ownership and activity

### Technical Features
- **Authentication**: Secure login/registration with Supabase Auth
- **File Storage**: Secure document storage with Supabase Storage
- **Google Drive Integration**: Optional Google Drive sync for documents
- **Real-time Updates**: Live updates between user and admin interfaces
- **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (Database, Auth, Storage, Edge Functions)
- **UI Components**: Shadcn/ui
- **Icons**: Lucide React
- **Deployment**: Vercel

## Setup Instructions

### 1. Supabase Setup

1. **Create a Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

2. **Configure Authentication**
   - In Supabase Dashboard, go to Authentication > Settings
   - Disable email confirmations for development: 
     - Set "Enable email confirmations" to OFF
   - Set up any additional auth providers if needed

3. **Set up Environment Variables**
   - Copy `.env.example` to `.env.local`
   - Add your Supabase URL and anon key:
   ```env
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   ```

### 2. Google Drive Integration (Optional)

1. **Enable Google Drive API**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create a new project or select existing
   - Enable Google Drive API
   - Create credentials (Service Account)

2. **Get Google Drive File IDs**
   - Upload files to Google Drive
   - Right-click file → Share → Copy link
   - Extract file ID from URL: `https://drive.google.com/file/d/FILE_ID/view`

### 3. Local Development

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```

3. **Access the Application**
   - Open [http://localhost:5173](http://localhost:5173)
   - Register a new admin account
   - Register a new user account
   - Test the document workflow

### 4. Demo Accounts

Register accounts with these credentials for testing:

**Admin Account:**
- Email: <EMAIL>
- Password: admin123
- Role: Admin

**User Account:**
- Email: <EMAIL>  
- Password: user123
- Role: User

### 5. Deployment to Vercel

1. **Connect GitHub Repository**
   - Push your code to GitHub
   - Connect repository to Vercel

2. **Configure Environment Variables**
   - In Vercel Dashboard, go to your project settings
   - Add environment variables:
     - `VITE_SUPABASE_URL`
     - `VITE_SUPABASE_ANON_KEY`

3. **Deploy**
   - Vercel will automatically deploy on git push
   - Your app will be available at `https://your-app.vercel.app`

## Usage Workflow

### For Users:
1. **Login** to your account
2. **Upload Document** - Click "Upload Document" button
3. **Fill Details** - Add subject, details, urgency, and optional Google Drive ID
4. **Track Status** - Monitor document in "Uploaded Documents" tab
5. **Download Signed** - Once signed, download from "Signed Documents" tab

### For Admins:
1. **Login** to admin account  
2. **Review Queue** - Check "Pending Signature" tab for new documents
3. **View/Download** - Review original documents
4. **Sign or Feedback** - Either upload signed version or provide feedback
5. **Track History** - View all signed documents in "Signed Documents" tab

## File Structure

```
src/
├── components/
│   ├── ui/                 # Shadcn UI components
│   ├── LoginPage.tsx       # Authentication interface
│   ├── UserDashboard.tsx   # User document management
│   ├── AdminDashboard.tsx  # Admin validation queue
│   └── UploadDocumentModal.tsx # Document upload modal
├── supabase/
│   └── functions/
│       └── server/
│           └── index.tsx   # Backend API server
├── App.tsx                 # Main application component
└── README.md              # This file
```

## API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `GET /auth/profile` - Get user profile

### Documents
- `POST /documents/upload` - Upload document
- `GET /documents/user` - Get user's documents
- `GET /documents/admin` - Get all documents (admin)
- `POST /documents/:id/sign` - Sign document (admin)
- `GET /documents/:id/download/:type` - Download document
- `POST /documents/:id/feedback` - Add feedback (admin)

## Security Features

- **Row Level Security**: Supabase RLS policies protect user data
- **File Access Control**: Private storage buckets with signed URLs
- **Role-based Access**: Admin and user role separation
- **Secure Authentication**: Supabase Auth with JWT tokens

## Troubleshooting

### Common Issues:

1. **Supabase Connection Error**
   - Check environment variables are correctly set
   - Verify Supabase project URL and keys
   - Ensure project is not paused

2. **File Upload Errors**
   - Check file size limits (10MB max)
   - Verify file types are supported
   - Check storage bucket permissions

3. **Authentication Issues**
   - Verify email confirmation settings
   - Check if users are being created in Supabase
   - Review auth configuration

### Getting Help

- Check browser console for error messages
- Review Supabase logs in dashboard
- Verify network requests in browser dev tools

## Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -m "Add feature"`
4. Push to branch: `git push origin feature-name`
5. Submit pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.