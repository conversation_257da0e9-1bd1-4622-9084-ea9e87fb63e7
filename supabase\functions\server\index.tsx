import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { cors } from 'npm:hono/cors'
import { Hono } from 'npm:hono'
import { logger } from 'npm:hono/logger'
import * as kv from './kv_store.tsx'

const app = new Hono()

// Middleware
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}))
app.use('*', logger(console.log))

// Initialize Supabase client for admin operations
const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
)

// Create private bucket for documents on startup
async function initializeBuckets() {
  try {
    const bucketName = 'make-77fbb41f-documents'
    const { data: buckets } = await supabase.storage.listBuckets()
    const bucketExists = buckets?.some(bucket => bucket.name === bucketName)
    
    if (!bucketExists) {
      const { error } = await supabase.storage.createBucket(bucketName, { public: false })
      if (error) console.error('Error creating bucket:', error)
      else console.log('Documents bucket created successfully')
    }
  } catch (error) {
    console.error('Error initializing buckets:', error)
  }
}

initializeBuckets()

// User registration
app.post('/make-server-77fbb41f/auth/register', async (c) => {
  try {
    const { email, password, name, role = 'user' } = await c.req.json()
    
    if (!email || !password || !name) {
      return c.json({ error: 'Missing required fields' }, 400)
    }

    const { data, error } = await supabase.auth.admin.createUser({
      email,
      password,
      user_metadata: { name, role },
      email_confirm: true // Auto-confirm since no email server configured
    })

    if (error) {
      console.error('Registration error:', error)
      return c.json({ error: error.message }, 400)
    }

    // Store user profile
    await kv.set(`user:${data.user.id}`, {
      id: data.user.id,
      email,
      name,
      role,
      created_at: new Date().toISOString()
    })

    return c.json({ 
      message: 'User registered successfully', 
      user: { id: data.user.id, email, name, role } 
    })
  } catch (error) {
    console.error('Registration error:', error)
    return c.json({ error: 'Registration failed' }, 500)
  }
})

// Get user profile
app.get('/make-server-77fbb41f/auth/profile', async (c) => {
  try {
    const accessToken = c.req.header('Authorization')?.split(' ')[1]
    if (!accessToken) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const { data: { user }, error } = await supabase.auth.getUser(accessToken)
    if (error || !user) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const profile = await kv.get(`user:${user.id}`)
    if (!profile) {
      return c.json({ error: 'Profile not found' }, 404)
    }

    return c.json({ user: profile })
  } catch (error) {
    console.error('Profile fetch error:', error)
    return c.json({ error: 'Failed to fetch profile' }, 500)
  }
})

// Upload document
app.post('/make-server-77fbb41f/documents/upload', async (c) => {
  try {
    const accessToken = c.req.header('Authorization')?.split(' ')[1]
    if (!accessToken) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const { data: { user }, error } = await supabase.auth.getUser(accessToken)
    if (error || !user) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const formData = await c.req.formData()
    const file = formData.get('file') as File
    const subject = formData.get('subject') as string
    const details = formData.get('details') as string
    const urgency = formData.get('urgency') as string
    const googleDriveId = formData.get('googleDriveId') as string

    if (!file || !subject) {
      return c.json({ error: 'File and subject are required' }, 400)
    }

    const documentId = crypto.randomUUID()
    const fileName = `${documentId}-${file.name}`
    
    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('make-77fbb41f-documents')
      .upload(`uploads/${fileName}`, file, {
        contentType: file.type,
        upsert: false
      })

    if (uploadError) {
      console.error('Upload error:', uploadError)
      return c.json({ error: 'Failed to upload file' }, 500)
    }

    // Create document metadata
    const document = {
      id: documentId,
      user_id: user.id,
      file_name: file.name,
      storage_path: uploadData.path,
      subject,
      details,
      urgency,
      google_drive_id: googleDriveId,
      status: 'pending',
      uploaded_at: new Date().toISOString(),
      signed_at: null,
      feedback: null
    }

    await kv.set(`document:${documentId}`, document)
    await kv.set(`user_documents:${user.id}:${documentId}`, documentId)

    return c.json({ message: 'Document uploaded successfully', document })
  } catch (error) {
    console.error('Document upload error:', error)
    return c.json({ error: 'Failed to upload document' }, 500)
  }
})

// Get documents for user
app.get('/make-server-77fbb41f/documents/user', async (c) => {
  try {
    const accessToken = c.req.header('Authorization')?.split(' ')[1]
    if (!accessToken) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const { data: { user }, error } = await supabase.auth.getUser(accessToken)
    if (error || !user) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const documentIds = await kv.getByPrefix(`user_documents:${user.id}:`)
    const documents = []

    for (const docId of documentIds) {
      const document = await kv.get(`document:${docId}`)
      if (document) {
        documents.push(document)
      }
    }

    return c.json({ documents })
  } catch (error) {
    console.error('Get user documents error:', error)
    return c.json({ error: 'Failed to fetch documents' }, 500)
  }
})

// Get all documents for admin
app.get('/make-server-77fbb41f/documents/admin', async (c) => {
  try {
    const accessToken = c.req.header('Authorization')?.split(' ')[1]
    if (!accessToken) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const { data: { user }, error } = await supabase.auth.getUser(accessToken)
    if (error || !user) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const userProfile = await kv.get(`user:${user.id}`)
    if (!userProfile || userProfile.role !== 'admin') {
      return c.json({ error: 'Admin access required' }, 403)
    }

    const documentIds = await kv.getByPrefix('document:')
    const documents = []

    for (const docData of documentIds) {
      if (docData && typeof docData === 'object') {
        documents.push(docData)
      }
    }

    return c.json({ documents })
  } catch (error) {
    console.error('Get admin documents error:', error)
    return c.json({ error: 'Failed to fetch documents' }, 500)
  }
})

// Sign document (admin only)
app.post('/make-server-77fbb41f/documents/:id/sign', async (c) => {
  try {
    const accessToken = c.req.header('Authorization')?.split(' ')[1]
    if (!accessToken) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const { data: { user }, error } = await supabase.auth.getUser(accessToken)
    if (error || !user) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const userProfile = await kv.get(`user:${user.id}`)
    if (!userProfile || userProfile.role !== 'admin') {
      return c.json({ error: 'Admin access required' }, 403)
    }

    const documentId = c.req.param('id')
    const formData = await c.req.formData()
    const signedFile = formData.get('signedFile') as File
    const signedGoogleDriveId = formData.get('signedGoogleDriveId') as string

    if (!signedFile) {
      return c.json({ error: 'Signed file is required' }, 400)
    }

    const document = await kv.get(`document:${documentId}`)
    if (!document) {
      return c.json({ error: 'Document not found' }, 404)
    }

    // Upload signed document
    const signedFileName = `signed-${documentId}-${signedFile.name}`
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('make-77fbb41f-documents')
      .upload(`signed/${signedFileName}`, signedFile, {
        contentType: signedFile.type,
        upsert: false
      })

    if (uploadError) {
      console.error('Signed upload error:', uploadError)
      return c.json({ error: 'Failed to upload signed file' }, 500)
    }

    // Update document status
    const updatedDocument = {
      ...document,
      status: 'signed',
      signed_at: new Date().toISOString(),
      signed_file_path: uploadData.path,
      signed_google_drive_id: signedGoogleDriveId,
      signed_by: user.id
    }

    await kv.set(`document:${documentId}`, updatedDocument)

    return c.json({ message: 'Document signed successfully', document: updatedDocument })
  } catch (error) {
    console.error('Document signing error:', error)
    return c.json({ error: 'Failed to sign document' }, 500)
  }
})

// Download document
app.get('/make-server-77fbb41f/documents/:id/download/:type', async (c) => {
  try {
    const accessToken = c.req.header('Authorization')?.split(' ')[1]
    if (!accessToken) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const { data: { user }, error } = await supabase.auth.getUser(accessToken)
    if (error || !user) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const documentId = c.req.param('id')
    const type = c.req.param('type') // 'original' or 'signed'
    
    const document = await kv.get(`document:${documentId}`)
    if (!document) {
      return c.json({ error: 'Document not found' }, 404)
    }

    const userProfile = await kv.get(`user:${user.id}`)
    const isAdmin = userProfile?.role === 'admin'
    const isOwner = document.user_id === user.id

    if (!isAdmin && !isOwner) {
      return c.json({ error: 'Access denied' }, 403)
    }

    const filePath = type === 'signed' ? document.signed_file_path : document.storage_path
    if (!filePath) {
      return c.json({ error: 'File not found' }, 404)
    }

    const { data, error: downloadError } = await supabase.storage
      .from('make-77fbb41f-documents')
      .createSignedUrl(filePath, 60) // 1 minute expiry

    if (downloadError) {
      console.error('Download error:', downloadError)
      return c.json({ error: 'Failed to generate download link' }, 500)
    }

    return c.json({ downloadUrl: data.signedUrl })
  } catch (error) {
    console.error('Document download error:', error)
    return c.json({ error: 'Failed to download document' }, 500)
  }
})

// Add feedback to document (admin only)
app.post('/make-server-77fbb41f/documents/:id/feedback', async (c) => {
  try {
    const accessToken = c.req.header('Authorization')?.split(' ')[1]
    if (!accessToken) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const { data: { user }, error } = await supabase.auth.getUser(accessToken)
    if (error || !user) {
      return c.json({ error: 'Unauthorized' }, 401)
    }

    const userProfile = await kv.get(`user:${user.id}`)
    if (!userProfile || userProfile.role !== 'admin') {
      return c.json({ error: 'Admin access required' }, 403)
    }

    const documentId = c.req.param('id')
    const { feedback } = await c.req.json()

    if (!feedback) {
      return c.json({ error: 'Feedback is required' }, 400)
    }

    const document = await kv.get(`document:${documentId}`)
    if (!document) {
      return c.json({ error: 'Document not found' }, 404)
    }

    const updatedDocument = {
      ...document,
      feedback,
      feedback_at: new Date().toISOString(),
      feedback_by: user.id
    }

    await kv.set(`document:${documentId}`, updatedDocument)

    return c.json({ message: 'Feedback added successfully', document: updatedDocument })
  } catch (error) {
    console.error('Add feedback error:', error)
    return c.json({ error: 'Failed to add feedback' }, 500)
  }
})

console.log('Document signing server started')
Deno.serve(app.fetch)