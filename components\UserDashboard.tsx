import { useState, useEffect } from 'react';
import { Search, ArrowLeft, RotateCcw, ExternalLink, FileText, Download, Upload, Plus, Eye, MessageSquare } from 'lucide-react';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback } from './ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Alert, AlertDescription } from './ui/alert';
import { UploadDocumentModal } from './UploadDocumentModal';

interface UserDashboardProps {
  user: any;
  onLogout: () => void;
}

export function UserDashboard({ user, onLogout }: UserDashboardProps) {
  const [documents, setDocuments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('date');

  useEffect(() => {
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/make-server-77fbb41f/documents/user`, {
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents);
      } else {
        setError('Failed to fetch documents');
      }
    } catch (err) {
      setError('Failed to fetch documents');
      console.error('Fetch documents error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpload = (newDocument: any) => {
    setDocuments(prev => [newDocument, ...prev]);
  };

  const handleDownload = async (documentId: string, type: 'original' | 'signed') => {
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/make-server-77fbb41f/documents/${documentId}/download/${type}`, {
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        window.open(data.downloadUrl, '_blank');
      } else {
        console.error('Download failed');
      }
    } catch (err) {
      console.error('Download error:', err);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-orange-500';
      case 'signed':
        return 'text-green-500';
      case 'feedback':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'neutral':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDocuments = documents.filter(doc =>
    doc.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.file_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const uploadedDocuments = filteredDocuments.filter(doc => doc.status !== 'signed');
  const signedDocuments = filteredDocuments.filter(doc => doc.status === 'signed');

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Navigation Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">D</span>
              </div>
              <span className="font-semibold text-gray-900">DocuSign</span>
            </div>
            
            <nav className="flex space-x-6">
              <a href="#" className="text-gray-900 font-medium">Dashboard</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">My Documents</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">Settings</a>
            </nav>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={() => fetchDocuments()}>
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">🔔</Button>
            <div className="flex items-center space-x-2">
              <Avatar className="w-8 h-8">
                <AvatarFallback>{user.name?.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">{user.name}</p>
                <p className="text-xs text-gray-500 capitalize">{user.role}</p>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={onLogout}>
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-semibold">Document Management</h1>
          </div>
          <Button onClick={() => setShowUploadModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Upload Document
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Filter Tabs */}
        <Tabs defaultValue="uploaded" className="mb-6">
          <TabsList>
            <TabsTrigger value="uploaded" className="flex items-center space-x-2">
              <span>Uploaded Documents</span>
              <Badge variant="secondary">{uploadedDocuments.length}</Badge>
            </TabsTrigger>
            <TabsTrigger value="signed" className="flex items-center space-x-2">
              <span>Signed Documents</span>
              <Badge variant="secondary">{signedDocuments.length}</Badge>
            </TabsTrigger>
          </TabsList>

          {/* Search and Sort */}
          <div className="flex items-center justify-between my-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input 
                placeholder="Search documents..." 
                className="pl-10 w-64" 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="subject">Subject</SelectItem>
                <SelectItem value="status">Status</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <TabsContent value="uploaded">
            {isLoading ? (
              <div className="text-center py-8">Loading documents...</div>
            ) : uploadedDocuments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No uploaded documents found. Upload your first document to get started.
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {uploadedDocuments.map((doc) => (
                  <div key={doc.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-500">ID</span>
                        <span className="text-sm font-semibold">#{doc.id.slice(-6)}</span>
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" onClick={() => handleDownload(doc.id, 'original')}>
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Subject</p>
                      <p className="text-sm font-medium">{doc.subject}</p>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Status</p>
                      <p className={`text-sm font-medium ${getStatusColor(doc.status)}`}>
                        {doc.status === 'pending' ? 'Pending Signature' : doc.status}
                      </p>
                    </div>

                    {doc.urgency && doc.urgency !== 'none' && (
                      <div className="mb-3">
                        <Badge className={getUrgencyColor(doc.urgency)}>
                          {doc.urgency}
                        </Badge>
                      </div>
                    )}

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Uploaded</p>
                      <p className="text-sm font-medium">
                        {new Date(doc.uploaded_at).toLocaleDateString()}
                      </p>
                    </div>

                    {doc.feedback && (
                      <div className="mb-3">
                        <p className="text-sm text-gray-500 mb-1">Feedback</p>
                        <div className="bg-blue-50 p-2 rounded">
                          <p className="text-sm text-blue-800">{doc.feedback}</p>
                        </div>
                      </div>
                    )}

                    <div>
                      <p className="text-sm text-gray-500 mb-2">File</p>
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center space-x-2">
                          <FileText className="w-4 h-4 text-blue-500" />
                          <div>
                            <p className="text-xs font-medium">{doc.file_name}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="signed">
            {signedDocuments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No signed documents yet.
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {signedDocuments.map((doc) => (
                  <div key={doc.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-500">ID</span>
                        <span className="text-sm font-semibold">#{doc.id.slice(-6)}</span>
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" onClick={() => handleDownload(doc.id, 'original')}>
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleDownload(doc.id, 'signed')}>
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Subject</p>
                      <p className="text-sm font-medium">{doc.subject}</p>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Status</p>
                      <Badge className="bg-green-100 text-green-800">Signed</Badge>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Signed Date</p>
                      <p className="text-sm font-medium text-green-600">
                        {new Date(doc.signed_at).toLocaleDateString()}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 mb-2">Files</p>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-blue-500" />
                            <span className="text-xs">Original</span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-green-500" />
                            <span className="text-xs">Signed</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      <UploadDocumentModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleUpload}
        userToken={user.access_token}
      />
    </div>
  );
}