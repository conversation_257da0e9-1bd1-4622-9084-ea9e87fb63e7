import { useState, useEffect } from 'react';
import { Search, ArrowLeft, RotateCcw, ExternalLink, FileText, Download, Upload, Eye, MessageSquare, CheckCircle } from 'lucide-react';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback } from './ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Alert, AlertDescription } from './ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from './ui/dialog';
import { Textarea } from './ui/textarea';
import { Label } from './ui/label';

interface AdminDashboardProps {
  user: any;
  onLogout: () => void;
}

export function AdminDashboard({ user, onLogout }: AdminDashboardProps) {
  const [documents, setDocuments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const [showSignModal, setShowSignModal] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/make-server-77fbb41f/documents/admin`, {
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents);
      } else {
        setError('Failed to fetch documents');
      }
    } catch (err) {
      setError('Failed to fetch documents');
      console.error('Fetch documents error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (documentId: string, type: 'original' | 'signed') => {
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/make-server-77fbb41f/documents/${documentId}/download/${type}`, {
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        window.open(data.downloadUrl, '_blank');
      } else {
        console.error('Download failed');
      }
    } catch (err) {
      console.error('Download error:', err);
    }
  };

  const handleSignDocument = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formData = new FormData(e.currentTarget);
    formData.append('documentId', selectedDocument.id);

    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/make-server-77fbb41f/documents/${selectedDocument.id}/sign`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
        },
        body: formData,
      });

      if (response.ok) {
        await fetchDocuments();
        setShowSignModal(false);
        setSelectedDocument(null);
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to sign document');
      }
    } catch (err) {
      setError('Failed to sign document');
      console.error('Sign document error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddFeedback = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formData = new FormData(e.currentTarget);
    const feedback = formData.get('feedback') as string;

    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/make-server-77fbb41f/documents/${selectedDocument.id}/feedback`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ feedback }),
      });

      if (response.ok) {
        await fetchDocuments();
        setShowFeedbackModal(false);
        setSelectedDocument(null);
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to add feedback');
      }
    } catch (err) {
      setError('Failed to add feedback');
      console.error('Add feedback error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-orange-500';
      case 'signed':
        return 'text-green-500';
      case 'feedback':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'neutral':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getUserInitials = (userId: string) => {
    // This would normally fetch user data, but for demo we'll use the ID
    return userId.slice(0, 2).toUpperCase();
  };

  const filteredDocuments = documents.filter(doc =>
    doc.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.file_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const pendingDocuments = filteredDocuments.filter(doc => doc.status === 'pending');
  const signedDocuments = filteredDocuments.filter(doc => doc.status === 'signed');

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Navigation Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">D</span>
              </div>
              <span className="font-semibold text-gray-900">DocuSign Admin</span>
            </div>
            
            <nav className="flex space-x-6">
              <a href="#" className="text-gray-900 font-medium">Validation Queue</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">All Documents</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">Users</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">Settings</a>
            </nav>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={() => fetchDocuments()}>
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">🔔</Button>
            <div className="flex items-center space-x-2">
              <Avatar className="w-8 h-8">
                <AvatarFallback>{user.name?.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">{user.name}</p>
                <p className="text-xs text-gray-500 capitalize">{user.role}</p>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={onLogout}>
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-6">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <h1 className="text-2xl font-semibold">Document Validation Queue</h1>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Filter Tabs */}
        <Tabs defaultValue="pending" className="mb-6">
          <TabsList>
            <TabsTrigger value="pending" className="flex items-center space-x-2">
              <span>Pending Signature</span>
              <Badge variant="secondary">{pendingDocuments.length}</Badge>
            </TabsTrigger>
            <TabsTrigger value="signed" className="flex items-center space-x-2">
              <span>Signed Documents</span>
              <Badge variant="secondary">{signedDocuments.length}</Badge>
            </TabsTrigger>
          </TabsList>

          {/* Search and Sort */}
          <div className="flex items-center justify-between my-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input 
                placeholder="Search documents..." 
                className="pl-10 w-64" 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="subject">Subject</SelectItem>
                <SelectItem value="urgency">Urgency</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <TabsContent value="pending">
            {isLoading ? (
              <div className="text-center py-8">Loading documents...</div>
            ) : pendingDocuments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No pending documents for signature.
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {pendingDocuments.map((doc) => (
                  <div key={doc.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-500">ID</span>
                        <span className="text-sm font-semibold">#{doc.id.slice(-6)}</span>
                      </div>
                      <div className="flex space-x-1">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleDownload(doc.id, 'original')}
                          title="View Document"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleDownload(doc.id, 'original')}
                          title="Download Document"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Subject</p>
                      <p className="text-sm font-medium">{doc.subject}</p>
                    </div>

                    {doc.details && (
                      <div className="mb-3">
                        <p className="text-sm text-gray-500 mb-1">Details</p>
                        <p className="text-xs text-gray-700">{doc.details}</p>
                      </div>
                    )}

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Status</p>
                      <Badge className="bg-orange-100 text-orange-800">Pending Signature</Badge>
                    </div>

                    {doc.urgency && doc.urgency !== 'none' && (
                      <div className="mb-3">
                        <Badge className={getUrgencyColor(doc.urgency)}>
                          {doc.urgency}
                        </Badge>
                      </div>
                    )}

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Submitted By</p>
                      <div className="flex items-center space-x-2">
                        <Avatar className="w-6 h-6">
                          <AvatarFallback className="text-xs">{getUserInitials(doc.user_id)}</AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium">User {doc.user_id.slice(-4)}</span>
                      </div>
                    </div>

                    <div className="mb-4">
                      <p className="text-sm text-gray-500 mb-1">Uploaded</p>
                      <p className="text-sm font-medium">
                        {new Date(doc.uploaded_at).toLocaleDateString()}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Button 
                        className="w-full" 
                        size="sm"
                        onClick={() => {
                          setSelectedDocument(doc);
                          setShowSignModal(true);
                        }}
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Sign Document
                      </Button>
                      <Button 
                        variant="outline" 
                        className="w-full" 
                        size="sm"
                        onClick={() => {
                          setSelectedDocument(doc);
                          setShowFeedbackModal(true);
                        }}
                      >
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Add Feedback
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="signed">
            {signedDocuments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No signed documents yet.
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {signedDocuments.map((doc) => (
                  <div key={doc.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-500">ID</span>
                        <span className="text-sm font-semibold">#{doc.id.slice(-6)}</span>
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" onClick={() => handleDownload(doc.id, 'original')}>
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleDownload(doc.id, 'signed')}>
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Subject</p>
                      <p className="text-sm font-medium">{doc.subject}</p>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Status</p>
                      <Badge className="bg-green-100 text-green-800">Signed</Badge>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Signed Date</p>
                      <p className="text-sm font-medium text-green-600">
                        {new Date(doc.signed_at).toLocaleDateString()}
                      </p>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500 mb-1">Owner</p>
                      <div className="flex items-center space-x-2">
                        <Avatar className="w-6 h-6">
                          <AvatarFallback className="text-xs">{getUserInitials(doc.user_id)}</AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium">User {doc.user_id.slice(-4)}</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 mb-2">Files</p>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-blue-500" />
                            <span className="text-xs">Original</span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-green-500" />
                            <span className="text-xs">Signed</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Sign Document Modal */}
      <Dialog open={showSignModal} onOpenChange={setShowSignModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Sign Document</DialogTitle>
            <DialogDescription>
              Upload the signed version of the document: {selectedDocument?.subject}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSignDocument} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="signedFile">Signed Document File *</Label>
              <Input
                id="signedFile"
                name="signedFile"
                type="file"
                accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
                required
                disabled={isSubmitting}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="signedGoogleDriveId">Signed Document Google Drive ID (Optional)</Label>
              <Input
                id="signedGoogleDriveId"
                name="signedGoogleDriveId"
                placeholder="Enter Google Drive file ID for the signed document"
                disabled={isSubmitting}
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setShowSignModal(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Signing...' : 'Complete Signing'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Feedback Modal */}
      <Dialog open={showFeedbackModal} onOpenChange={setShowFeedbackModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add Feedback</DialogTitle>
            <DialogDescription>
              Provide feedback for the document: {selectedDocument?.subject}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleAddFeedback} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="feedback">Feedback Message *</Label>
              <Textarea
                id="feedback"
                name="feedback"
                placeholder="Enter your feedback or request additional information..."
                rows={4}
                required
                disabled={isSubmitting}
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setShowFeedbackModal(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Adding...' : 'Add Feedback'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}